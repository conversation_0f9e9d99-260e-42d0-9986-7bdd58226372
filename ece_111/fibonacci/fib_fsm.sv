`timescale 1ns/1ps
module fib_fsm (
    input logic clk,
    input logic rst_n,
    input logic start,
    input logic [9:0] n,
    output logic done,
    output logic [31:0] fib_out
);
    typedef enum {  
        IDLE,
        CALCULATING,
        DONE
    } state_t;
    
    state_t current_state, next_state;
    logic [9:0] count;
    logic [31:0] fib_a, fib_b;  // Current two Fibonacci numbers
    
    // State transition logic
    always_comb begin : next_state_logic
        case (current_state)
            IDLE: begin
                if (start) begin
                    if (n < 2)
                        next_state = DONE;
                    else
                        next_state = CALCULATING;
                end else
                    next_state = IDLE;
            end
            CALCULATING: begin
                if (count >= n)
                    next_state = DONE;
                else
                    next_state = CALCULATING;
            end
            DONE: begin
                if (start) begin
                    if (n < 2)
                        next_state = DONE;
                    else
                        next_state = CALCULATING;
                end else
                    next_state = IDLE;
            end
            default: next_state = IDLE;
        endcase
    end
    
    // State register
    always_ff @(posedge clk or negedge rst_n) begin : state_register
        if (!rst_n)
            current_state <= IDLE;
        else
            current_state <= next_state;
    end
    
    // Data path logic
    always_ff @(posedge clk or negedge rst_n) begin : data_registers
        if (!rst_n) begin
            count <= 2;
            fib_a <= 0;
            fib_b <= 1;
        end else begin
            case (current_state)
                IDLE: begin
                    if (start) begin
                        count <= 2;
                        fib_a <= 0;  // F(0) = 0
                        fib_b <= 1;  // F(1) = 1
                    end
                end
                CALCULATING: begin
                    if (count <= n) begin
                        count <= count + 1;
                        // Calculate next Fibonacci: fib_a becomes old fib_b, fib_b becomes fib_a + fib_b
                        fib_b <= fib_a + fib_b;
                        fib_a <= fib_b;
                    end
                end
                DONE: begin
                    if (start) begin
                        count <= 2;
                        fib_a <= 0;  // F(0) = 0
                        fib_b <= 1;  // F(1) = 1
                    end
                end
            endcase
        end
    end
    
    // Output logic
    assign done = (current_state == DONE);
    assign fib_out = (n==0||n==1) ? n : fib_b;  // fib_b contains F(count)

endmodule
