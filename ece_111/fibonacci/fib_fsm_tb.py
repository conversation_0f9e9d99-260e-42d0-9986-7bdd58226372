import cocotb
from cocotb.triggers import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Timer,ReadOnly
from cocotb.clock import Clock
from cocotb.binary import BinaryValue
import os
import random
from pathlib import Path

from cocotb.runner import get_runner


# Fibonacci reference model
def fib_golden_ref(n):
    if n <= 1:
        return n
    else:
        return fib_golden_ref(n - 1) + fib_golden_ref(n - 2)


async def reset_dut(dut):
    """Reset the DUT"""
    dut.rst_n.value = 0
    dut.start.value = 0
    dut.n.value = 0
    await RisingEdge(dut.clk)
    await RisingEdge(dut.clk)
    dut.rst_n.value = 1
    await RisingEdge(dut.clk)


async def start_calculation(dut, n_val):
    """Start a Fibonacci calculation"""
    dut.n.value = n_val
    dut.start.value = 1
    await RisingEdge(dut.clk)
    dut.start.value = 0


async def wait_for_done(dut, timeout_cycles=1000):
    """Wait for calculation to complete"""
    cycles = 0
    while not dut.done.value and cycles < timeout_cycles:
        await RisingEdge(dut.clk)
        cycles += 1
    
    if cycles >= timeout_cycles:
        raise Exception(f"Timeout waiting for done signal after {timeout_cycles} cycles")
    
    return cycles


@cocotb.test()
async def test_fib_fsm_basic(dut):
    """Test basic functionality of fib_fsm module"""
    
    dut._log.info("Starting basic test for fib_fsm module")
    
    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Reset
    await reset_dut(dut)
    
    # Test cases for small values
    test_cases = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20]
    
    for n_val in test_cases:
        dut._log.info(f"Testing n = {n_val}")
        
        # Start calculation
        await start_calculation(dut, n_val)
        
        # Wait for completion
        cycles = await wait_for_done(dut)
        
        # Check result
        expected_output = fib_golden_ref(n_val)
        actual_output = int(dut.fib_out.value)
        
        dut._log.info(f"n={n_val}: Expected={expected_output}, Got={actual_output}, Cycles={cycles}")
        
        assert actual_output == expected_output, \
            f"Fibonacci({n_val}): expected {expected_output}, got {actual_output}"
        


@cocotb.test()
async def test_fib_fsm_edge_cases(dut):
    """Test edge cases and special scenarios"""
    
    dut._log.info("Starting edge case tests for fib_fsm module")
    
    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Reset
    await reset_dut(dut)
    
    # Test case 1: F(0)
    dut._log.info("Testing F(0)")
    await start_calculation(dut, 0)
    await wait_for_done(dut)
    assert int(dut.fib_out.value) == 0, f"F(0) should be 0, got {int(dut.fib_out.value)}"
    
    # Test case 2: F(1)
    dut._log.info("Testing F(1)")
    await start_calculation(dut, 1)
    await wait_for_done(dut)
    assert int(dut.fib_out.value) == 1, f"F(1) should be 1, got {int(dut.fib_out.value)}"
    
    # Test case 3: Multiple consecutive calculations
    dut._log.info("Testing consecutive calculations")
    for n_val in [3, 7, 12]:
        await start_calculation(dut, n_val)
        await wait_for_done(dut)
        expected = fib_golden_ref(n_val)
        actual = int(dut.fib_out.value)
        assert actual == expected, f"F({n_val}): expected {expected}, got {actual}"


@cocotb.test()
async def test_fib_fsm_restart_during_calculation(dut):
    """Test restarting calculation while one is in progress"""
    
    dut._log.info("Starting restart test for fib_fsm module")
    
    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Reset
    await reset_dut(dut)
    
    # Start a long calculation
    await start_calculation(dut, 15)
    
    # Wait a few cycles, then restart with different value
    for _ in range(5):
        await RisingEdge(dut.clk)
    
    # Restart with new value
    await start_calculation(dut, 8)
    await wait_for_done(dut)
    
    # Should get F(8)
    expected = fib_golden_ref(8)
    actual = int(dut.fib_out.value)
    assert actual == expected, f"After restart: expected {expected}, got {actual}"





@cocotb.test()
async def test_fib_fsm_performance(dut):
    """Test larger Fibonacci numbers for performance"""
    
    dut._log.info("Starting performance test for fib_fsm module")
    
    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Reset
    await reset_dut(dut)
    
    # Test larger values (limited by 32-bit output)
    # F(30) = 832040, F(35) = 9227465 (still fits in 32 bits)
    test_cases = [25, 30, 35]
    
    for n_val in test_cases:
        dut._log.info(f"Performance test: n = {n_val}")
        
        await start_calculation(dut, n_val)
        cycles = await wait_for_done(dut)
        
        expected_output = fib_golden_ref(n_val)
        actual_output = int(dut.fib_out.value)
        
        dut._log.info(f"F({n_val}): Expected={expected_output}, Got={actual_output}")
        dut._log.info(f"Calculation took {cycles} cycles")
        
        assert actual_output == expected_output, \
            f"F({n_val}): expected {expected_output}, got {actual_output}"


def test_run():
    """Run the test suite"""
    tests_dir = Path(__file__).parent
    print(f"Running tests from: {tests_dir}")
    hdl_dir = tests_dir
    sim = os.getenv("SIM", "icarus")
    runner = get_runner(sim)
    runner.build(
        verilog_sources=[hdl_dir / "fib_fsm.sv"],
        hdl_toplevel="fib_fsm",
        parameters={},
        waves=True,
        build_dir=f"sim_build/fib_fsm"
    )
    runner.test(
        test_module="fib_fsm_tb",
        hdl_toplevel="fib_fsm",
        parameters={},
        extra_env={}
    )


if __name__ == "__main__":
    test_run()