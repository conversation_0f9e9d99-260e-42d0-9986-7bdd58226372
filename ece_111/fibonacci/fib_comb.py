import cocotb
from cocotb.triggers import Timer
from cocotb.binary import BinaryValue
import os
import random
from pathlib import Path

from cocotb.runner import get_runner


#fib ref model
def fib_golden_ref(n):
    if n <= 1:
        return n
    else:
        return fib_golden_ref(n - 1) + fib_golden_ref(n - 2)


@cocotb.test()
async def test_fib_comb(dut):
    """测试fib_comb模块"""
    
    dut._log.info("Starting test for fib_comb module")

    #测试向量
    for i in range(2,31):
        # 设置输入
        dut.n.value = i
        
        # 等待组合逻辑稳定
        await Timer(1, units="ps")
        
        # 计算期望输出
        expected_output = fib_golden_ref(i)
        
        # 验证输出
        dut._log.info(f"Input: {i}, Expected: {expected_output}, Got: {int(dut.fib_out.value)}")
        assert int(dut.fib_out.value) == expected_output, f"Output mismatch: expected {expected_output}, got {int(dut.fib_out.value)}"

    

def test_run():
    tests_dir = Path(__file__).parent
    print(f"Running tests from: {tests_dir}")
    hdl_dir = tests_dir
    sim = os.getenv("SIM", "icarus")
    runner = get_runner(sim)
    runner.build(
        verilog_sources=[hdl_dir / "fibonacci_comb.sv"],
        hdl_toplevel="fibonacci_comb",
        parameters={},
        waves=True,
        build_dir=f"sim_build/fibonacci_comb"
    )
    runner.test(
        test_module="fib_comb",
        hdl_toplevel="fibonacci_comb",
        parameters={},
        extra_env={}
    )

if __name__ == "__main__":
    test_run()