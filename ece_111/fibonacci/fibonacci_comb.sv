`timescale 1ns/1ps
module fibon<PERSON><PERSON>_comb(
    input logic [9:0] n,
    output logic [31:0] fib_out
);

    localparam  F0 = 0;
    localparam F1 = 1;


    // <PERSON><PERSON><PERSON><PERSON> calculation using combinational logic
    logic [31:0] fib_n_1 = F1,fib_n_2 = F0;
    logic [31:0] fib_n;
    always_comb begin : cal_fib
        fib_n = 0;
        fib_n_1 = F1;
        fib_n_2 = F0;
        for (int i=2;i<=n;i++) begin
            fib_n = fib_n_1 + fib_n_2;
            fib_n_2 = fib_n_1;
            fib_n_1 = fib_n;
        end
        fib_out = fib_n;
    end
       
endmodule

