     -.--ns INFO     gpi                                ..mbed/gpi_embed.cpp:108  in set_program_name_in_venv        Using Python virtual environment interpreter at /home/<USER>/py_venv/venv/bin/python
     -.--ns INFO     gpi                                ../gpi/GpiCommon.cpp:101  in gpi_print_registered_impl       VPI registered
     0.00ns INFO     cocotb                             Running on Icarus Verilog version 12.0 (stable)
     0.00ns INFO     cocotb                             Running tests with cocotb v1.9.2 from /home/<USER>/py_venv/venv/lib/python3.12/site-packages/cocotb
     0.00ns INFO     cocotb                             Seeding Python random module with 1750431972
     0.00ns INFO     cocotb.regression                  Found test fib_fsm_tb.test_fib_fsm_basic
     0.00ns INFO     cocotb.regression                  Found test fib_fsm_tb.test_fib_fsm_edge_cases
     0.00ns INFO     cocotb.regression                  Found test fib_fsm_tb.test_fib_fsm_restart_during_calculation
     0.00ns INFO     cocotb.regression                  Found test fib_fsm_tb.test_fib_fsm_performance
     0.00ns INFO     cocotb.regression                  running test_fib_fsm_basic (1/4)
                                                          Test basic functionality of fib_fsm module
     0.00ns INFO     cocotb.fib_fsm                     Starting basic test for fib_fsm module
    20.00ns INFO     cocotb.fib_fsm                     Testing n = 0
    50.00ns INFO     cocotb.fib_fsm                     n=0: Expected=0, Got=0, Cycles=2
    70.00ns INFO     cocotb.fib_fsm                     Testing n = 1
   110.00ns INFO     cocotb.fib_fsm                     n=1: Expected=1, Got=1, Cycles=3
   130.00ns INFO     cocotb.fib_fsm                     Testing n = 2
   180.00ns INFO     cocotb.fib_fsm                     n=2: Expected=1, Got=1, Cycles=4
   200.00ns INFO     cocotb.fib_fsm                     Testing n = 3
   260.00ns INFO     cocotb.fib_fsm                     n=3: Expected=2, Got=2, Cycles=5
   280.00ns INFO     cocotb.fib_fsm                     Testing n = 4
   350.00ns INFO     cocotb.fib_fsm                     n=4: Expected=3, Got=3, Cycles=6
   370.00ns INFO     cocotb.fib_fsm                     Testing n = 5
   450.00ns INFO     cocotb.fib_fsm                     n=5: Expected=5, Got=5, Cycles=7
   470.00ns INFO     cocotb.fib_fsm                     Testing n = 6
   560.00ns INFO     cocotb.fib_fsm                     n=6: Expected=8, Got=8, Cycles=8
   580.00ns INFO     cocotb.fib_fsm                     Testing n = 7
   680.00ns INFO     cocotb.fib_fsm                     n=7: Expected=13, Got=13, Cycles=9
   700.00ns INFO     cocotb.fib_fsm                     Testing n = 8
   810.00ns INFO     cocotb.fib_fsm                     n=8: Expected=21, Got=21, Cycles=10
   830.00ns INFO     cocotb.fib_fsm                     Testing n = 9
   950.00ns INFO     cocotb.fib_fsm                     n=9: Expected=34, Got=34, Cycles=11
   970.00ns INFO     cocotb.fib_fsm                     Testing n = 10
  1100.00ns INFO     cocotb.fib_fsm                     n=10: Expected=55, Got=55, Cycles=12
  1120.00ns INFO     cocotb.fib_fsm                     Testing n = 15
  1300.00ns INFO     cocotb.fib_fsm                     n=15: Expected=610, Got=610, Cycles=17
  1320.00ns INFO     cocotb.fib_fsm                     Testing n = 20
  1550.00ns INFO     cocotb.fib_fsm                     n=20: Expected=6765, Got=6765, Cycles=22
  1570.00ns INFO     cocotb.regression                  test_fib_fsm_basic passed
  1570.00ns INFO     cocotb.regression                  running test_fib_fsm_edge_cases (2/4)
                                                          Test edge cases and special scenarios
  1570.00ns INFO     cocotb.fib_fsm                     Starting edge case tests for fib_fsm module
  1600.00ns INFO     cocotb.fib_fsm                     Testing F(0)
  1630.00ns INFO     cocotb.fib_fsm                     Testing F(1)
  1670.00ns INFO     cocotb.fib_fsm                     Testing consecutive calculations
  1980.00ns INFO     cocotb.regression                  test_fib_fsm_edge_cases passed
  1980.00ns INFO     cocotb.regression                  running test_fib_fsm_restart_during_calculation (3/4)
                                                          Test restarting calculation while one is in progress
  1980.00ns INFO     cocotb.fib_fsm                     Starting restart test for fib_fsm module
  2120.00ns INFO     cocotb.regression                  test_fib_fsm_restart_during_calculation passed
  2120.00ns INFO     cocotb.regression                  running test_fib_fsm_performance (4/4)
                                                          Test larger Fibonacci numbers for performance
  2120.00ns INFO     cocotb.fib_fsm                     Starting performance test for fib_fsm module
  2150.00ns INFO     cocotb.fib_fsm                     Performance test: n = 25
  2430.00ns INFO     cocotb.fib_fsm                     F(25): Expected=75025, Got=75025
  2430.00ns INFO     cocotb.fib_fsm                     Calculation took 27 cycles
  2430.00ns INFO     cocotb.fib_fsm                     Performance test: n = 30
  2760.00ns INFO     cocotb.fib_fsm                     F(30): Expected=832040, Got=832040
  2760.00ns INFO     cocotb.fib_fsm                     Calculation took 32 cycles
  2760.00ns INFO     cocotb.fib_fsm                     Performance test: n = 35
  3140.00ns INFO     cocotb.fib_fsm                     F(35): Expected=9227465, Got=9227465
  3140.00ns INFO     cocotb.fib_fsm                     Calculation took 37 cycles
  3140.00ns INFO     cocotb.regression                  test_fib_fsm_performance passed
  3140.00ns INFO     cocotb.regression                  ************************************************************************************************************
                                                        ** TEST                                                STATUS  SIM TIME (ns)  REAL TIME (s)  RATIO (ns/s) **
                                                        ************************************************************************************************************
                                                        ** fib_fsm_tb.test_fib_fsm_basic                        PASS        1570.00           0.01     133929.82  **
                                                        ** fib_fsm_tb.test_fib_fsm_edge_cases                   PASS         410.00           0.00      82549.39  **
                                                        ** fib_fsm_tb.test_fib_fsm_restart_during_calculation   PASS         140.00           0.00     111487.90  **
                                                        ** fib_fsm_tb.test_fib_fsm_performance                  PASS        1020.00           0.93       1098.58  **
                                                        ************************************************************************************************************
                                                        ** TESTS=4 PASS=4 FAIL=0 SKIP=0                                     3140.00           1.32       2384.42  **
                                                        ************************************************************************************************************
                                                        
VCD info: dumpfile /home/<USER>/project/fpga_design_element/sim_build/fib_fsm/fib_fsm.fst opened for output.
Running tests from: /home/<USER>/project/fpga_design_element/ece_111/fibonacci
WARNING: Skipping compilation of /home/<USER>/project/fpga_design_element/sim_build/fib_fsm/sim.vvp
INFO: Running command vvp -M /home/<USER>/py_venv/venv/lib/python3.12/site-packages/cocotb/libs -m libcocotbvpi_icarus /home/<USER>/project/fpga_design_element/sim_build/fib_fsm/sim.vvp in directory /home/<USER>/project/fpga_design_element/sim_build/fib_fsm
INFO: Results file: /home/<USER>/project/fpga_design_element/sim_build/fib_fsm/results.xml
