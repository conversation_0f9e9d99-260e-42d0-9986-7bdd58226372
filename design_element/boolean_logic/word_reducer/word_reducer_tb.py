import cocotb
from cocotb.triggers import Timer
from cocotb.binary import BinaryValue
import os
import random

WORD_WIDTH = int(os.environ.get("WORD_WIDTH", "32"))
WORD_CNT = int(os.environ.get("WORD_CNT", "4"))
TOTAL_WIDTH = WORD_WIDTH * WORD_CNT

operation_map = {
    "AND": 0,
    "NAND": 1,
    "OR": 2,
    "NOR": 3,
    "XOR": 4,
    "XNOR": 5
}

# Helper function to calculate the expected bit reduction based on operation string
def calculate_bit_reduction(op_str, bits_list):
    if not bits_list:
        return 0 
    
    if op_str == "AND":
        result = 1
        for b in bits_list:
            result &= b
        return result
    elif op_str == "NAND":
        result = 1
        for b in bits_list:
            result &= b
        return 1 - result
    elif op_str == "OR":
        result = 0
        for b in bits_list:
            result |= b
        return result
    elif op_str == "NOR":
        result = 0
        for b in bits_list:
            result |= b
        return 1 - result
    elif op_str == "XOR":
        result = 0
        for b in bits_list:
            result ^= b
        return result
    elif op_str == "XNOR":
        result = 0
        for b in bits_list:
            result ^= b
        return 1 - result
    else:
        # This case should ideally not be reached if op_str is from operation_map
        return 0 # Default case as per Verilog

@cocotb.test()
async def test_word_reducer(dut):
    """测试word_reducer模块"""
    # 记录测试的参数配置
    dut._log.info(f"Testing with WORD_WIDTH={WORD_WIDTH}, WORD_CNT={WORD_CNT}")
    
    # Iterate through each operation, using its string name for logic and its integer value for DUT input
    for op_str, op_val in operation_map.items(): 
        dut.op_in.value = op_val # Assign the integer value corresponding to the operation
        for _ in range(10):
            input_value = random.randint(0, (2**TOTAL_WIDTH)-1)
            dut.words_in.value = input_value
            await Timer(1, units="ns")
            expected_output = 0
            for j in range(WORD_WIDTH): # Iterate through each bit position within a word
                # Collect the j-th bit from each of the WORD_CNT words
                bits_for_reduction = []
                for i in range(WORD_CNT): # Iterate through each word
                    # Extract the j-th bit of the i-th word
                    # The i-th word starts at bit i * WORD_WIDTH
                    # The j-th bit of the i-th word is at index (i * WORD_WIDTH) + j
                    bit_val = (input_value >> (i * WORD_WIDTH + j)) & 1
                    bits_for_reduction.append(bit_val)
                
                # Calculate the reduced bit for this position 'j' using the helper function
                reduced_bit = calculate_bit_reduction(op_str, bits_for_reduction)
                
                # Place the reduced bit into the correct position in expected_output
                expected_output |= (reduced_bit << j)
            
            # Verify output
            dut._log.info(f"Operation: {op_str}, Input: 0x{input_value:x}, Expected: 0x{expected_output:x}, Got: 0x{int(dut.words_out.value):x}")
            assert int(dut.words_out.value) == expected_output, \
                f"Operation: {op_str}, Input: 0x{input_value:x}, Output mismatch: expected 0x{expected_output:x}, got 0x{int(dut.words_out.value):x}"

                