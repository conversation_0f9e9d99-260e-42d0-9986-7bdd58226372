import logging
import pytest
import os
import sys
from pathlib import Path
from itertools import product
import cocotb
# 导入cocotb runner
from cocotb.runner import get_runner

# 定义要测试的参数组合
WORD_WIDTH_VALUES = [8, 16, 32]
WORD_CNT_VALUES = [1, 2, 4, 8]

# 创建测试ID生成函数
def idfn(val):
    if isinstance(val, list):
        return '-'.join(str(x) for x in val)
    else:
        return str(val)


# 参数化测试函数
@pytest.mark.parametrize("word_width", WORD_WIDTH_VALUES, ids=idfn)
@pytest.mark.parametrize("word_cnt", WORD_CNT_VALUES, ids=idfn)
def test_word_reducer_parametrized(word_width, word_cnt):
    """为每个参数组合运行测试"""
    
    # 获取当前文件的目录
    tests_dir = Path(__file__).parent
    hdl_root_dir = tests_dir.parent 
    
    sim = os.getenv("SIM", "icarus")
    # 定义参数
    parameters = {
        "WORD_WIDTH": str(word_width),
        "WORD_CNT": str(word_cnt)
    }
    
    extra_env = {
        "WORD_WIDTH": str(word_width),
        "WORD_CNT": str(word_cnt)
    }
    # 使用cocotb runner运行测试
    runner = get_runner(sim)
    runner.build(
        verilog_sources=[
            hdl_root_dir / "word_reducer" / "word_reducer.sv",
            hdl_root_dir / "bit_reducer" / "bit_reducer.sv" 
        ],
        includes = [hdl_root_dir / "include"],
        hdl_toplevel="word_reducer",
        parameters=parameters,
        waves=True,
        build_dir=f"sim_build/word_reducer_W{word_width}_C{word_cnt}"
    )

    runner.test(
        test_module="word_reducer_tb",
        hdl_toplevel="word_reducer",
        parameters=parameters,
        extra_env=extra_env
    )

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])