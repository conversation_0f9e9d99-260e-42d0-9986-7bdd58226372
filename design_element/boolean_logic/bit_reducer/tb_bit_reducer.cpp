#include <iostream>
#include <vector>
#include <verilated.h>
#include <verilated_vcd_c.h>
#include "bit_reducer_lib.h" // Generated header

// Define the enum values corresponding to the SystemVerilog enum
// These values should match the enum in bit_reducer.sv
typedef enum {
    AND = 0,
    NAND = 1,
    OR = 2,
    NOR = 3,
    XOR = 4,
    XNOR = 5
} operation_e;

// Helper function to get operation name string
const char* get_operation_name(operation_e op) {
    switch (op) {
        case AND: return "AND";
        case NAND: return "NAND";
        case OR: return "OR";
        case NOR: return "NOR";
        case XOR: return "XOR";
        case XNOR: return "XNOR";
        default: return "UNKNOWN";
    }
}

int main(int argc, char** argv) {
    // Initialize Verilators
    Verilated::commandArgs(argc, argv);
    Verilated::traceEverOn(true);

    // Instantiate the module
    bit_reducer_lib* top = new bit_reducer_lib;

    // Setup tracing
    // VerilatedVcdC* tfp = new VerilatedVcdC;
    // top->trace(tfp, 99); // Trace 99 levels of hierarchy
    // tfp->open("bit_reducer_tb.vcd");

    // Define test inputs based on BIT_WIDTH (assuming BIT_WIDTH is accessible, e.g., via /*verilator public*/)
    // Alternatively, BIT_WIDTH could be passed as a command line argument to the testbench
    const int BIT_WIDTH = 8; // Assuming a default or known BIT_WIDTH for the testbench

    std::vector<unsigned int> test_inputs;
    // Add some basic test cases
    test_inputs.push_back(0); // All zeros
    if (BIT_WIDTH > 0) {
        test_inputs.push_back(~0U >> (32 - BIT_WIDTH)); // All ones (mask to BIT_WIDTH)
    }
    if (BIT_WIDTH >= 2) {
        unsigned int alternating_01 = 0;
        for(int i = 0; i < BIT_WIDTH; ++i) {
            if (i % 2 != 0) alternating_01 |= (1U << i);
        }
        test_inputs.push_back(alternating_01); // Alternating 0101...
        unsigned int alternating_10 = ~alternating_01 & (~0U >> (32 - BIT_WIDTH)); // Alternating 1010...
        test_inputs.push_back(alternating_10);
    }
    // Add more comprehensive test cases if needed, e.g., single bit set, single bit clear

    // Iterate through all operations
    for (int op_val = AND; op_val <= XNOR; ++op_val) {
        operation_e current_operation = static_cast<operation_e>(op_val);
        std::cout << "Testing operation: " << get_operation_name(current_operation) << std::endl;

        // Apply test inputs for the current operation
        for (unsigned int input_val : test_inputs) {
            top->op_in = current_operation; // Drive the op_in input
            top->bits_in = input_val;

            // Evaluate the model
            top->eval();

            // Dump trace
            // tfp->dump(Verilated::time());
            Verilated::timeInc(1); // Advance time

            // Calculate expected output
            bool expected_output;
            switch (current_operation) { // Use current_operation
                case AND: {
                    bool all_ones = true;
                    for(int i = 0; i < BIT_WIDTH; ++i) {
                        if (!((input_val >> i) & 1)) {
                            all_ones = false;
                            break;
                        }
                    }
                    expected_output = all_ones;
                    break;
                }
                case NAND: {
                     bool all_ones = true;
                    for(int i = 0; i < BIT_WIDTH; ++i) {
                        if (!((input_val >> i) & 1)) {
                            all_ones = false;
                            break;
                        }
                    }
                    expected_output = !all_ones;
                    break;
                }
                case OR: {
                    bool any_ones = false;
                    for(int i = 0; i < BIT_WIDTH; ++i) {
                        if ((input_val >> i) & 1) {
                            any_ones = true;
                            break;
                        }
                    }
                    expected_output = any_ones;
                    break;
                }
                case NOR: {
                    bool any_ones = false;
                    for(int i = 0; i < BIT_WIDTH; ++i) {
                        if ((input_val >> i) & 1) {
                            any_ones = true;
                            break;
                        }
                    }
                    expected_output = !any_ones;
                    break;
                }
                case XOR: {
                    int set_bits = 0;
                    for(int i = 0; i << BIT_WIDTH; ++i) { // Corrected loop condition
                        if ((input_val >> i) & 1) {
                            set_bits++;
                        }
                    }
                    expected_output = (set_bits % 2 != 0);
                    break;
                }
                case XNOR: {
                     int set_bits = 0;
                    for(int i = 0; i << BIT_WIDTH; ++i) { // Corrected loop condition
                        if ((input_val >> i) & 1) {
                            set_bits++;
                        }
                    }
                    expected_output = (set_bits % 2 == 0);
                    break;
                }
                default:
                    std::cerr << "Error: Unknown operation " << get_operation_name(current_operation) << std::endl;
                    expected_output = false; // Default to false for unknown operation
                    break;
            }

            // Check output
            if (top->bit_out != expected_output) {
                std::cerr << "Error for operation " << get_operation_name(current_operation) << ", Input 0x" << std::hex << input_val
                          << ", Expected " << expected_output << ", Got " << top->bit_out << std::endl;
            } else {
                 std::cout << "Success for operation " << get_operation_name(current_operation) << ", Input 0x" << std::hex << input_val
                          << ", Expected " << expected_output << ", Got " << top->bit_out << std::endl;
            }
        }
    }


    // Close trace file
    // tfp->close();

    // Clean up model
    delete top;
    // delete tfp;

    // Final model cleanup
    Verilated::runFlushCallbacks();

    std::cout << "Simulation finished." << std::endl;

    return 0;
}