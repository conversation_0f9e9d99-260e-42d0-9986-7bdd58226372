#include <iostream>
#include <verilated.h>
#include "constant_lib.h"  // Generated by Verilator

int main(int argc, char** argv) {
    // Initialize Verilator
    Verilated::commandArgs(argc, argv);

    // Create an instance of our module under test
    constant_lib* tb = new constant_lib;

    // Simulate for a few cycles
    for (int i = 0; i < 10; i++) {
        // Evaluate the model
        tb->eval();

        // Print the output value
        std::cout << "Cycle " << i << ": constant_out = " << (unsigned int)tb->constant_out << std::endl;
    }

    // Check if the output matches the expected value
    // The expected value is defined by xmake through the EXPECTED_VALUE macro
#ifndef EXPECTED_VALUE
    #define EXPECTED_VALUE 42  // Default value if not defined
#endif

    if (tb->constant_out == EXPECTED_VALUE) {
        std::cout << "Test PASSED: Output value is " << EXPECTED_VALUE << " as expected" << std::endl;
    } else {
        std::cout << "Test FAILED: Output value is " << (unsigned int)tb->constant_out
                  << ", expected " << EXPECTED_VALUE << std::endl;
        return 1;
    }

    // Clean up
    delete tb;
    return 0;
}
