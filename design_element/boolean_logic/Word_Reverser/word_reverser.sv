`timescale 1ns/1ps

module word_reverser #(
    parameter WORD_WIDTH = 32,
    parameter WORD_CNT = 4,
    parameter  TOTAL_WIDTH = WORD_WIDTH * WORD_CNT
) (
    input [TOTAL_WIDTH-1:0] word_in,
    output [TOTAL_WIDTH-1:0] word_out
);

    generate
        genvar i;
        for (i = 0; i < WORD_CNT; i = i + 1) begin : word_reverser_logic
            assign word_out[i*WORD_WIDTH +: WORD_WIDTH] = word_in[(WORD_CNT-1-i)*WORD_WIDTH +: WORD_WIDTH];
        end
    endgenerate
    
endmodule
