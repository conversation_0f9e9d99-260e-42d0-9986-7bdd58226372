import cocotb
from cocotb.triggers import Timer
from cocotb.binary import BinaryValue
import os
import random

# 从环境变量获取参数
WORD_WIDTH = int(os.environ.get("WORD_WIDTH", "32"))
WORD_CNT = int(os.environ.get("WORD_CNT", "4"))
TOTAL_WIDTH = WORD_WIDTH * WORD_CNT

@cocotb.test()
async def test_word_reverser(dut):
    """测试word_reverser模块"""
    
    # 记录测试的参数配置
    dut._log.info(f"Testing with WORD_WIDTH={WORD_WIDTH}, WORD_CNT={WORD_CNT}")
    
    # 随机生成测试向量
    for _ in range(10):  # 进行10次随机测试
        # 创建随机输入
        input_value = random.randint(0, (2**TOTAL_WIDTH)-1)
        dut.word_in.value = input_value
        
        # 等待组合逻辑稳定
        await Timer(1, units="ns")
        
        # 计算期望输出
        expected_output = 0
        for i in range(WORD_CNT):
            # 提取输入中的一个字
            word_mask = (2**WORD_WIDTH - 1) << (WORD_WIDTH * (WORD_CNT - 1 - i))
            word = (input_value & word_mask) >> (WORD_WIDTH * (WORD_CNT - 1 - i))
            # 放置到输出的正确位置
            expected_output |= word << (WORD_WIDTH * i)
        
        # 验证输出
        dut._log.info(f"Input: 0x{input_value:x}, Expected: 0x{expected_output:x}, Got: 0x{int(dut.word_out.value):x}")
        assert int(dut.word_out.value) == expected_output, f"Output mismatch: expected 0x{expected_output:x}, got 0x{int(dut.word_out.value):x}"